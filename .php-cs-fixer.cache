{"php": "8.4.10", "version": "3.78.0:v3.78.0#effd5edddae2fc202028f63e515db21815100fd2", "indent": "    ", "lineEnding": "\n", "rules": {"binary_operator_spaces": {"default": "at_least_single_space"}, "blank_line_after_opening_tag": true, "blank_line_between_import_groups": true, "blank_lines_before_namespace": true, "braces_position": {"allow_single_line_empty_anonymous_classes": true}, "class_definition": {"inline_constructor_arguments": false, "space_before_parenthesis": true}, "compact_nullable_type_declaration": true, "declare_equal_normalize": true, "lowercase_cast": true, "lowercase_static_reference": true, "new_with_parentheses": true, "no_blank_lines_after_class_opening": true, "no_extra_blank_lines": {"tokens": ["use"]}, "no_leading_import_slash": true, "no_whitespace_in_blank_line": true, "ordered_class_elements": {"order": ["use_trait"]}, "ordered_imports": {"imports_order": ["class", "function", "const"], "sort_algorithm": "none"}, "return_type_declaration": true, "short_scalar_cast": true, "single_import_per_statement": {"group_to_single_imports": false}, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "const_import", "do", "else", "elseif", "final", "finally", "for", "foreach", "function", "function_import", "if", "insteadof", "interface", "namespace", "new", "private", "protected", "public", "static", "switch", "trait", "try", "use", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "single_trait_insert_per_statement": true, "ternary_operator_spaces": true, "unary_operator_spaces": {"only_dec_inc": true}, "visibility_required": true, "blank_line_after_namespace": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"attribute_placement": "ignore", "on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_line_after_imports": true, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "encoding": true, "full_opening_tag": true}, "hashes": {"/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder8/database/migrations/2025_07_04_005557_add_slugs_to_users_table.php": "1f1b00fbddb606ceb3725ac27a22320a", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder6/database/migrations/2025_07_08_203916_create_categories_table.php": "dca2574633b8eaef3b7fb0f25a4549fa", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder7/database/migrations/2025_07_08_204736_create_category_closure_triggers.php": "2b4f09372d8a8e7755ba9ed7a6307d18", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder5/database/migrations/2025_07_08_204736_create_category_closure_triggers.php": "33af2f66733f34b17718d81ff0c3a769", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder3/database/migrations/2025_07_08_204737_create_categorizables_table.php": "d082fc73e13e02e2b8b7ea600e31b475", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder2114/database/sqldump/seeders/ArtistSeeder.php": "71f5842fcd969cb0d56665d8306fedef", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder881/database/sqldump/seeders/GenreSeeder.php": "d9227a8f5543b4e0643fd1b63cd0ed2b", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder9/database/sqldump/seeders/MediaTypeSeeder.php": "ac09397a3d33b4ce075f7a42d525988b", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder329/database/sqldump/seeders/PlaylistSeeder.php": "4bbb480744f3f87ef3109ea7e5649880", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder2980/database/sqldump/seeders/AlbumSeeder.php": "3b02331b76c8db3ebf61e46b42b0ee54", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder1651/database/sqldump/seeders/EmployeeSeeder.php": "75f46cae024c5bb71a2aa8cc8dd83534", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder2630/database/sqldump/seeders/ChinookDatabaseSeeder.php": "b046a77792bd0f599bbb084dd5a07b4b", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder2301/database/sqldump/seeders/AlbumSeeder.php": "2f42bb67c13f58132dc687e0db13791f", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder1894/test_album_seeder.php": "504e23d11dea15a3313783d746d7e358", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder1430/test_album_seeder.php": "504e23d11dea15a3313783d746d7e358", "/private/var/folders/f0/2m6r6db57bxglzky2b_f49n00000gn/T/PHP CS Fixertemp_folder2312/.ai/tools/test_album_seeder.php": "504e23d11dea15a3313783d746d7e358"}}