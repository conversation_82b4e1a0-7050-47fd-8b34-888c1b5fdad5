<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/workos-sac/database
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/workos-sac/database/seeders/WorldSeeder.php' => 
    array (
      0 => 'f3fe7f35a69afdd635d2bbdd281594a67f67c6f8',
      1 => 
      array (
        0 => 'database\\seeders\\worldseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/UserSeeder.php' => 
    array (
      0 => 'f32a0893366d496b0428847a697be5ff487109ec',
      1 => 
      array (
        0 => 'database\\seeders\\userseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/DatabaseSeeder.php' => 
    array (
      0 => '67b94164c8142650e6e950b2ae33587add96f37f',
      1 => 
      array (
        0 => 'database\\seeders\\databaseseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/UserFactory.php' => 
    array (
      0 => '87cd78e0123655d681657cb90cc86e37f11ab327',
      1 => 
      array (
        0 => 'database\\factories\\userfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\unverified',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/PlaylistSeeder.php' => 
    array (
      0 => '89fcb6b720854ea9ac14468818b7d1d229db3473',
      1 => 
      array (
        0 => 'database\\seeders\\playlistseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/CategorySeeder.php' => 
    array (
      0 => '7bc6aa29a24a417fc047008e3dba644c6fba6c2f',
      1 => 
      array (
        0 => 'database\\seeders\\categoryseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
        1 => 'database\\seeders\\creategenrecategories',
        2 => 'database\\seeders\\createmoodcategories',
        3 => 'database\\seeders\\createthemecategories',
        4 => 'database\\seeders\\createeracategories',
        5 => 'database\\seeders\\createinstrumentcategories',
        6 => 'database\\seeders\\createlanguagecategories',
        7 => 'database\\seeders\\createoccasioncategories',
        8 => 'database\\seeders\\createcategoryhierarchy',
        9 => 'database\\seeders\\createsimplecategories',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/MediaTypeSeeder.php' => 
    array (
      0 => 'd9a22fa5fac91e7867794376fe35f324f9672582',
      1 => 
      array (
        0 => 'database\\seeders\\mediatypeseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/TrackSeeder.php' => 
    array (
      0 => '04b0441610fb1bcb8c956c97f67667609336d6de',
      1 => 
      array (
        0 => 'database\\seeders\\trackseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/InvoiceSeeder.php' => 
    array (
      0 => 'f4722f7c56bfeae1f7350fcf5b878ffed55b2a41',
      1 => 
      array (
        0 => 'database\\seeders\\invoiceseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/CategorizableSeeder.php' => 
    array (
      0 => 'fa7c17b66829595eacbfd95ee892b7809d4d1ee1',
      1 => 
      array (
        0 => 'database\\seeders\\categorizableseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/AlbumSeeder.php' => 
    array (
      0 => 'eb9bf61476e68ae32fe1f8daf3b363c173509493',
      1 => 
      array (
        0 => 'database\\seeders\\albumseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/ArtistSeeder.php' => 
    array (
      0 => 'b8ae4c8fdc390d7c5bd491f8be158de12d8df668',
      1 => 
      array (
        0 => 'database\\seeders\\artistseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/InvoiceLineSeeder.php' => 
    array (
      0 => '361d86bf7f003a4e6b8d6cddbd12c91d28bc421c',
      1 => 
      array (
        0 => 'database\\seeders\\invoicelineseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/PermissionSeeder.php' => 
    array (
      0 => '5250a2effb3c0918bfcc301687403c45b474cefe',
      1 => 
      array (
        0 => 'database\\seeders\\permissionseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/CustomerSeeder.php' => 
    array (
      0 => '354196fe858d08186f91bf61b2f1dffdacd0dc8e',
      1 => 
      array (
        0 => 'database\\seeders\\customerseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/EmployeeSeeder.php' => 
    array (
      0 => '2634fbf8d6b96aa8133eb9bf3a3d4a6bdcd9051c',
      1 => 
      array (
        0 => 'database\\seeders\\employeeseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/ChinookDatabaseSeeder.php' => 
    array (
      0 => '39094e67f8a6c2dae838ee6ebd30c22fad9de441',
      1 => 
      array (
        0 => 'database\\seeders\\chinookdatabaseseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/PlaylistTrackSeeder.php' => 
    array (
      0 => 'cb2bcfbd97f8d798ca1761b751e657082738c16c',
      1 => 
      array (
        0 => 'database\\seeders\\playlisttrackseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/MediaTypeFactory.php' => 
    array (
      0 => '61c445c979ae36240060f6fd503e91a7247b12f1',
      1 => 
      array (
        0 => 'database\\factories\\mediatypefactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/InvoiceLineFactory.php' => 
    array (
      0 => 'aa39b2707fd85f91073e75620b1e73227b2b76ef',
      1 => 
      array (
        0 => 'database\\factories\\invoicelinefactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/CategoryFactory.php' => 
    array (
      0 => '0440e9c73842310deba197452a12c416fdaa7d6f',
      1 => 
      array (
        0 => 'database\\factories\\categoryfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\getcategorydatabytype',
        2 => 'database\\factories\\configure',
        3 => 'database\\factories\\oftype',
        4 => 'database\\factories\\active',
        5 => 'database\\factories\\inactive',
        6 => 'database\\factories\\childof',
        7 => 'database\\factories\\root',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/AlbumFactory.php' => 
    array (
      0 => '5416a84a45b688c2ca0e82c56b58bf3e49a697de',
      1 => 
      array (
        0 => 'database\\factories\\albumfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\forartist',
        2 => 'database\\factories\\classicrock',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/TrackFactory.php' => 
    array (
      0 => '229f433cb93174c187db1c82f626e0c639487618',
      1 => 
      array (
        0 => 'database\\factories\\trackfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\foralbum',
        2 => 'database\\factories\\short',
        3 => 'database\\factories\\long',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/PlaylistFactory.php' => 
    array (
      0 => 'b5a7ffff4d94b16862fbb07ca6df292ded909428',
      1 => 
      array (
        0 => 'database\\factories\\playlistfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/CustomerFactory.php' => 
    array (
      0 => '4ea9edb21840576d912c4e68a17d178b5863dbb2',
      1 => 
      array (
        0 => 'database\\factories\\customerfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\withsupportrep',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/InvoiceFactory.php' => 
    array (
      0 => '382f070ee080ba2bc20835acebbe637721e84fe9',
      1 => 
      array (
        0 => 'database\\factories\\invoicefactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\forcustomer',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/EmployeeFactory.php' => 
    array (
      0 => '9d5142b0bb377739d30363f0799b820b77d321bc',
      1 => 
      array (
        0 => 'database\\factories\\employeefactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\manager',
        2 => 'database\\factories\\reportsto',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/factories/ArtistFactory.php' => 
    array (
      0 => '265844bacedaffcbf799035126a011e422789bce',
      1 => 
      array (
        0 => 'database\\factories\\artistfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\configure',
        2 => 'database\\factories\\rock',
        3 => 'database\\factories\\jazz',
        4 => 'database\\factories\\popular',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/seeders/RoleSeeder.php' => 
    array (
      0 => 'eee57c11cccf484882597d11e17f6e82384f421c',
      1 => 
      array (
        0 => 'database\\seeders\\roleseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/ArtistSeeder.php' => 
    array (
      0 => '4abb1e93a440a9ebcb75e09b6580104a2cd51d23',
      1 => 
      array (
        0 => 'database\\seeders\\artistseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/GenreSeeder.php' => 
    array (
      0 => '36fe9e036485d3951183fa3bcc4e8e3ce0b1c79c',
      1 => 
      array (
        0 => 'database\\seeders\\genreseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/MediaTypeSeeder.php' => 
    array (
      0 => 'd9a22fa5fac91e7867794376fe35f324f9672582',
      1 => 
      array (
        0 => 'database\\seeders\\mediatypeseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/PlaylistSeeder.php' => 
    array (
      0 => '07f1dbe8c8808f08c4bd219895a7262abe19f766',
      1 => 
      array (
        0 => 'database\\seeders\\playlistseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/AlbumSeeder.php' => 
    array (
      0 => '15daf45419fb1518842d0df61e23ae098225ac11',
      1 => 
      array (
        0 => 'database\\seeders\\albumseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/EmployeeSeeder.php' => 
    array (
      0 => '2eda863433e0f3014479c36aa290042fc298c2cd',
      1 => 
      array (
        0 => 'database\\seeders\\employeeseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/database/sqldump/seeders/ChinookDatabaseSeeder.php' => 
    array (
      0 => '614f05cd971c7ff9df1b5b6e6f7cb75924b96fef',
      1 => 
      array (
        0 => 'database\\seeders\\chinookdatabaseseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
  ),
));