<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/workos-sac/app
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/workos-sac/app/Providers/AppServiceProvider.php' => 
    array (
      0 => '5b3911e46542e5a7977d214aeb7d3002ab0efcda',
      1 => 
      array (
        0 => 'app\\providers\\appserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\configurecarbon',
        3 => 'app\\providers\\configurecommands',
        4 => 'app\\providers\\configuremodels',
        5 => 'app\\providers\\configurepassworddefaults',
        6 => 'app\\providers\\configuresqlitedb',
        7 => 'app\\providers\\configureurl',
        8 => 'app\\providers\\configurevite',
        9 => 'app\\providers\\configureworkos',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/VoltServiceProvider.php' => 
    array (
      0 => '65a26a795005f380bc32bceec5c24b770dc58dbc',
      1 => 
      array (
        0 => 'app\\providers\\voltserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/FolioServiceProvider.php' => 
    array (
      0 => '8cd87d7f787d11d68c1ff62e60629e3a9a97d365',
      1 => 
      array (
        0 => 'app\\providers\\folioserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/User.php' => 
    array (
      0 => 'd01737af8d4442218b5b85d99e477097f42d1be0',
      1 => 
      array (
        0 => 'app\\models\\user',
      ),
      2 => 
      array (
        0 => 'app\\models\\casts',
        1 => 'app\\models\\getauthpassword',
        2 => 'app\\models\\getsecondarykeytype',
        3 => 'app\\models\\initials',
        4 => 'app\\models\\getslugoptions',
        5 => 'app\\models\\getroutekeyname',
        6 => 'app\\models\\getactivitylogoptions',
        7 => 'app\\models\\gettotalcommentscount',
        8 => 'app\\models\\getrecentcomments',
        9 => 'app\\models\\subscribetoallcomments',
        10 => 'app\\models\\subscribetorepliesonly',
        11 => 'app\\models\\issubscribedtocomments',
        12 => 'app\\models\\getcommentsubscriptions',
        13 => 'app\\models\\gettotalreactionscount',
        14 => 'app\\models\\getfavoritereaction',
        15 => 'app\\models\\hasreactedtocomment',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Http/Controllers/Controller.php' => 
    array (
      0 => '75cadca8afa5982965d1ac316df3c693271b4902',
      1 => 
      array (
        0 => 'app\\http\\controllers\\controller',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Enums/SecondaryKeyType.php' => 
    array (
      0 => '999884fa02a91ed3b04101d5a58c4583b7de98fa',
      1 => 
      array (
        0 => 'app\\enums\\secondarykeytype',
      ),
      2 => 
      array (
        0 => 'app\\enums\\default',
        1 => 'app\\enums\\getlabel',
        2 => 'app\\enums\\getcolor',
        3 => 'app\\enums\\getdescription',
        4 => 'app\\enums\\geticon',
        5 => 'app\\enums\\usecases',
        6 => 'app\\enums\\storageinfo',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/Filament/AdminPanelProvider.php' => 
    array (
      0 => 'bcfdba7de2f2d5ec1a81abb6379f4c7506a77b00',
      1 => 
      array (
        0 => 'app\\providers\\filament\\adminpanelprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\filament\\panel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Traits/HasSecondaryUniqueKey.php' => 
    array (
      0 => '6734eb7a6735b1d33c13404d813e6d08bfba4c60',
      1 => 
      array (
        0 => 'app\\traits\\hassecondaryuniquekey',
      ),
      2 => 
      array (
        0 => 'app\\traits\\boothassecondaryuniquekey',
        1 => 'app\\traits\\findbysecondarykey',
        2 => 'app\\traits\\findbysecondarykeyorfail',
        3 => 'app\\traits\\generatesecondarykey',
        4 => 'app\\traits\\getsecondarykeytype',
        5 => 'app\\traits\\setsecondarykeytype',
        6 => 'app\\traits\\getsecondarykeycolumn',
        7 => 'app\\traits\\scopebysecondarykey',
        8 => 'app\\traits\\getroutekeyname',
        9 => 'app\\traits\\getkeytypeinfo',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Traits/Categorizable.php' => 
    array (
      0 => 'a9005c3609b08ba32991c5f41a9808b498acd451',
      1 => 
      array (
        0 => 'app\\traits\\categorizable',
      ),
      2 => 
      array (
        0 => 'app\\traits\\categories',
        1 => 'app\\traits\\categoriesbytype',
        2 => 'app\\traits\\attachcategory',
        3 => 'app\\traits\\attachcategories',
        4 => 'app\\traits\\synccategoriesbytype',
        5 => 'app\\traits\\scopewithcategories',
        6 => 'app\\traits\\scopewithcategorytypes',
        7 => 'app\\traits\\scopewithoutcategories',
        8 => 'app\\traits\\hascategorytype',
        9 => 'app\\traits\\getcategorynames',
        10 => 'app\\traits\\getcategoriesbytypenames',
        11 => 'app\\traits\\getprimarycategory',
        12 => 'app\\traits\\setprimarycategory',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Enums/CategoryType.php' => 
    array (
      0 => '764c1396e0b47f7d28838c16eb5f42f390a219de',
      1 => 
      array (
        0 => 'app\\enums\\categorytype',
      ),
      2 => 
      array (
        0 => 'app\\enums\\label',
        1 => 'app\\enums\\color',
        2 => 'app\\enums\\icon',
        3 => 'app\\enums\\validationrules',
        4 => 'app\\enums\\defaultcategories',
        5 => 'app\\enums\\toarray',
        6 => 'app\\enums\\formodel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Category.php' => 
    array (
      0 => '08f03a12a0ca98802e6f837f0331cc75d13c541a',
      1 => 
      array (
        0 => 'app\\models\\category',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\parents',
        5 => 'app\\models\\parent',
        6 => 'app\\models\\children',
        7 => 'app\\models\\descendants',
        8 => 'app\\models\\ancestors',
        9 => 'app\\models\\siblings',
        10 => 'app\\models\\root',
        11 => 'app\\models\\scopeleaves',
        12 => 'app\\models\\scoperoots',
        13 => 'app\\models\\scopeoftype',
        14 => 'app\\models\\scopeactive',
        15 => 'app\\models\\scopeatdepth',
        16 => 'app\\models\\artists',
        17 => 'app\\models\\albums',
        18 => 'app\\models\\tracks',
        19 => 'app\\models\\playlists',
        20 => 'app\\models\\customers',
        21 => 'app\\models\\makechildof',
        22 => 'app\\models\\makeroot',
        23 => 'app\\models\\removefromhierarchy',
        24 => 'app\\models\\updatedescendantancestors',
        25 => 'app\\models\\wouldcreatecircularreference',
        26 => 'app\\models\\getfullnameattribute',
        27 => 'app\\models\\gettypelabelattribute',
        28 => 'app\\models\\gettypecolorattribute',
        29 => 'app\\models\\gettypeiconattribute',
        30 => 'app\\models\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/InvoiceLine.php' => 
    array (
      0 => '11a760083780b659d1e11bfa728424886656cc38',
      1 => 
      array (
        0 => 'app\\models\\invoiceline',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\invoice',
        5 => 'app\\models\\track',
        6 => 'app\\models\\getlinetotalattribute',
        7 => 'app\\models\\getformattedlinetotalattribute',
        8 => 'app\\models\\scopeaboveamount',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Playlist.php' => 
    array (
      0 => 'b73fcf4489d13233f3de2f2853f7e7e203e47ae9',
      1 => 
      array (
        0 => 'app\\models\\playlist',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\tracks',
        5 => 'app\\models\\scopepublic',
        6 => 'app\\models\\scopewithtracks',
        7 => 'app\\models\\gettrackcountattribute',
        8 => 'app\\models\\gettotaldurationattribute',
        9 => 'app\\models\\getformatteddurationattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/MediaType.php' => 
    array (
      0 => '641b26dc7df1a3bfadc07cfc1f0f0457b28494f2',
      1 => 
      array (
        0 => 'app\\models\\mediatype',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Track.php' => 
    array (
      0 => '43013103f59255b28a6c5c51e67c22aa8e90bf07',
      1 => 
      array (
        0 => 'app\\models\\track',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\album',
        5 => 'app\\models\\mediatype',
        6 => 'app\\models\\artist',
        7 => 'app\\models\\invoicelines',
        8 => 'app\\models\\playlists',
        9 => 'app\\models\\categories',
        10 => 'app\\models\\genres',
        11 => 'app\\models\\moods',
        12 => 'app\\models\\themes',
        13 => 'app\\models\\instruments',
        14 => 'app\\models\\languages',
        15 => 'app\\models\\occasions',
        16 => 'app\\models\\scopebygenre',
        17 => 'app\\models\\scopebymood',
        18 => 'app\\models\\scopebytheme',
        19 => 'app\\models\\scopebyinstrument',
        20 => 'app\\models\\scopebylanguage',
        21 => 'app\\models\\scopebyoccasion',
        22 => 'app\\models\\scopebyduration',
        23 => 'app\\models\\scopepopular',
        24 => 'app\\models\\scopeactive',
        25 => 'app\\models\\synccategoriesbytype',
        26 => 'app\\models\\getdurationattribute',
        27 => 'app\\models\\getfilesizeattribute',
        28 => 'app\\models\\getprimarygenreattribute',
        29 => 'app\\models\\getfullnameattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Customer.php' => 
    array (
      0 => 'abf4da9cfc7e301d73ccab818c070e73c93c5214',
      1 => 
      array (
        0 => 'app\\models\\customer',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\supportrep',
        5 => 'app\\models\\invoices',
        6 => 'app\\models\\scopebycountry',
        7 => 'app\\models\\scoperecentcustomers',
        8 => 'app\\models\\getfullnameattribute',
        9 => 'app\\models\\gettotalspentattribute',
        10 => 'app\\models\\getinvoicecountattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Invoice.php' => 
    array (
      0 => '600e4ef9a8105e502646f903bf08c12b1dc5aad8',
      1 => 
      array (
        0 => 'app\\models\\invoice',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\customer',
        5 => 'app\\models\\invoicelines',
        6 => 'app\\models\\scopebydaterange',
        7 => 'app\\models\\scopeaboveamount',
        8 => 'app\\models\\getlinecountattribute',
        9 => 'app\\models\\getformattedtotalattribute',
        10 => 'app\\models\\calculatetotal',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Employee.php' => 
    array (
      0 => '4e0c11dc31be8bdccea6cc3fa3fdc3b269e49ff6',
      1 => 
      array (
        0 => 'app\\models\\employee',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\manager',
        5 => 'app\\models\\subordinates',
        6 => 'app\\models\\customers',
        7 => 'app\\models\\scopemanagers',
        8 => 'app\\models\\scopebytitle',
        9 => 'app\\models\\getfullnameattribute',
        10 => 'app\\models\\getyearsofserviceattribute',
        11 => 'app\\models\\getcustomercountattribute',
        12 => 'app\\models\\getismanagerattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Album.php' => 
    array (
      0 => '28df0697fc4e605c82588d82faf4e9344fcb8381',
      1 => 
      array (
        0 => 'app\\models\\album',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\artist',
        5 => 'app\\models\\tracks',
        6 => 'app\\models\\categories',
        7 => 'app\\models\\genres',
        8 => 'app\\models\\moods',
        9 => 'app\\models\\themes',
        10 => 'app\\models\\eras',
        11 => 'app\\models\\languages',
        12 => 'app\\models\\scopebygenre',
        13 => 'app\\models\\scopebymood',
        14 => 'app\\models\\scopebyera',
        15 => 'app\\models\\scopewithtracks',
        16 => 'app\\models\\scopeactive',
        17 => 'app\\models\\synccategoriesbytype',
        18 => 'app\\models\\getfulltitleattribute',
        19 => 'app\\models\\gettotaldurationattribute',
        20 => 'app\\models\\getprimarygenreattribute',
        21 => 'app\\models\\getformatteddurationattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Artist.php' => 
    array (
      0 => '6de95920121f6be47c395d2cd44d59db82cbb75c',
      1 => 
      array (
        0 => 'app\\models\\artist',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\casts',
        2 => 'app\\models\\getslugoptions',
        3 => 'app\\models\\getroutekeyname',
        4 => 'app\\models\\albums',
        5 => 'app\\models\\tracks',
        6 => 'app\\models\\categories',
        7 => 'app\\models\\genres',
        8 => 'app\\models\\eras',
        9 => 'app\\models\\instruments',
        10 => 'app\\models\\scopebygenre',
        11 => 'app\\models\\scopebyera',
        12 => 'app\\models\\scopepublished',
        13 => 'app\\models\\scopeactive',
        14 => 'app\\models\\attachcategory',
        15 => 'app\\models\\synccategoriesbytype',
        16 => 'app\\models\\getcategoriesbytype',
        17 => 'app\\models\\getdisplaynameattribute',
        18 => 'app\\models\\getprimarygenreattribute',
        19 => 'app\\models\\getyearsactiveattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Analytics/PlayEvent.php' => 
    array (
      0 => 'ccffc1a1e03827a9413442841a8444692cb27f9b',
      1 => 
      array (
        0 => 'app\\models\\analytics\\playevent',
      ),
      2 => 
      array (
        0 => 'app\\models\\analytics\\getsecondarykeytype',
        1 => 'app\\models\\analytics\\casts',
        2 => 'app\\models\\analytics\\getslugoptions',
        3 => 'app\\models\\analytics\\getroutekeyname',
        4 => 'app\\models\\analytics\\track',
        5 => 'app\\models\\analytics\\user',
        6 => 'app\\models\\analytics\\customer',
        7 => 'app\\models\\analytics\\playlist',
        8 => 'app\\models\\analytics\\album',
        9 => 'app\\models\\analytics\\artist',
        10 => 'app\\models\\analytics\\source',
        11 => 'app\\models\\analytics\\scopecompleted',
        12 => 'app\\models\\analytics\\scopeskipped',
        13 => 'app\\models\\analytics\\scopebydaterange',
        14 => 'app\\models\\analytics\\scopebydevice',
        15 => 'app\\models\\analytics\\scopepopulartracks',
        16 => 'app\\models\\analytics\\getiscompletedattribute',
        17 => 'app\\models\\analytics\\getisskippedattribute',
        18 => 'app\\models\\analytics\\getformatteddurationattribute',
        19 => 'app\\models\\analytics\\recordplay',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Analytics/SearchEvent.php' => 
    array (
      0 => '700d8e5b74650e6de02d54907fc721a02326f6ec',
      1 => 
      array (
        0 => 'app\\models\\analytics\\searchevent',
      ),
      2 => 
      array (
        0 => 'app\\models\\analytics\\getsecondarykeytype',
        1 => 'app\\models\\analytics\\casts',
        2 => 'app\\models\\analytics\\getslugoptions',
        3 => 'app\\models\\analytics\\getroutekeyname',
        4 => 'app\\models\\analytics\\user',
        5 => 'app\\models\\analytics\\customer',
        6 => 'app\\models\\analytics\\scopewithresults',
        7 => 'app\\models\\analytics\\scopewithoutresults',
        8 => 'app\\models\\analytics\\scopewithclicks',
        9 => 'app\\models\\analytics\\scopebydaterange',
        10 => 'app\\models\\analytics\\scopebysearchtype',
        11 => 'app\\models\\analytics\\scopepopularqueries',
        12 => 'app\\models\\analytics\\gethasresultsattribute',
        13 => 'app\\models\\analytics\\gethasclickattribute',
        14 => 'app\\models\\analytics\\getclickthroughrateattribute',
        15 => 'app\\models\\analytics\\recordsearch',
        16 => 'app\\models\\analytics\\normalizequery',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/Analytics/ViewEvent.php' => 
    array (
      0 => '1e93070f8e0b9e3eeaee5c45bc7a2a23ccce0f01',
      1 => 
      array (
        0 => 'app\\models\\analytics\\viewevent',
      ),
      2 => 
      array (
        0 => 'app\\models\\analytics\\getsecondarykeytype',
        1 => 'app\\models\\analytics\\casts',
        2 => 'app\\models\\analytics\\getslugoptions',
        3 => 'app\\models\\analytics\\getroutekeyname',
        4 => 'app\\models\\analytics\\viewable',
        5 => 'app\\models\\analytics\\user',
        6 => 'app\\models\\analytics\\customer',
        7 => 'app\\models\\analytics\\scopeformodel',
        8 => 'app\\models\\analytics\\scopeuniqueviews',
        9 => 'app\\models\\analytics\\scopeengaged',
        10 => 'app\\models\\analytics\\scopebydaterange',
        11 => 'app\\models\\analytics\\scopebydevice',
        12 => 'app\\models\\analytics\\scopepopularcontent',
        13 => 'app\\models\\analytics\\getisengagedattribute',
        14 => 'app\\models\\analytics\\getformatteddurationattribute',
        15 => 'app\\models\\analytics\\recordview',
        16 => 'app\\models\\analytics\\updateengagement',
      ),
      3 => 
      array (
      ),
    ),
  ),
));