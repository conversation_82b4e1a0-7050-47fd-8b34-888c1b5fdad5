<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use Wildside\Userstamps\Userstamps;

class Employee extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'employees';

    /**
     * Get the secondary key type for this model.
     * Using ULID for employees - good for HR management.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'last_name',
        'first_name',
        'title',
        'reports_to',
        'birth_date',
        'hire_date',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
            'hire_date' => 'date',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the manager that this employee reports to.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'reports_to');
    }

    /**
     * Get the employees that report to this employee.
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'reports_to');
    }

    /**
     * Get the customers assigned to this employee.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'support_rep_id');
    }

    /**
     * Scope to find managers (employees with subordinates).
     */
    public function scopeManagers($query)
    {
        return $query->whereHas('subordinates');
    }

    /**
     * Scope to find employees by title.
     */
    public function scopeByTitle($query, string $title)
    {
        return $query->where('title', 'like', "%{$title}%");
    }

    /**
     * Get the employee's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * Get the employee's years of service.
     */
    public function getYearsOfServiceAttribute(): int
    {
        return $this->hire_date ? $this->hire_date->diffInYears(now()) : 0;
    }

    /**
     * Get the employee's customer count.
     */
    public function getCustomerCountAttribute(): int
    {
        return $this->customers()->count();
    }

    /**
     * Check if this employee is a manager.
     */
    public function getIsManagerAttribute(): bool
    {
        return $this->subordinates()->exists();
    }
}
