<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\Categorizable;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use Wildside\Userstamps\Userstamps;

class Artist extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;
    use Categorizable;

    /**
     * The table associated with the model.
     */
    protected $table = 'artists';

    /**
     * Get the secondary key type for this model.
     * Using ULID for artists - good balance of readability and performance.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'biography',
        'website',
        'social_links',
        'country',
        'formed_year',
        'is_active',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'social_links' => 'array',
            'formed_year' => 'integer',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the albums for the artist.
     */
    public function albums(): HasMany
    {
        return $this->hasMany(Album::class);
    }

    /**
     * Get all tracks for this artist through albums.
     */
    public function tracks(): HasMany
    {
        return $this->hasManyThrough(Track::class, Album::class);
    }

    /**
     * Polymorphic relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['created_by', 'updated_by']);
    }

    /**
     * Get genres (categories of type GENRE).
     */
    public function genres(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::GENRE);
    }

    /**
     * Get eras (categories of type ERA).
     */
    public function eras(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::ERA);
    }

    /**
     * Get instruments (categories of type INSTRUMENT).
     */
    public function instruments(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::INSTRUMENT);
    }

    /**
     * Scope to find artists by genre.
     */
    public function scopeByGenre(Builder $query, string $genreName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($genreName) {
            $q->where('type', CategoryType::GENRE)
                ->where('name', 'like', "%{$genreName}%");
        });
    }

    /**
     * Scope to find artists by era.
     */
    public function scopeByEra(Builder $query, string $eraName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($eraName) {
            $q->where('type', CategoryType::ERA)
                ->where('name', 'like', "%{$eraName}%");
        });
    }

    /**
     * Scope to find published artists (those with albums).
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->whereHas('albums');
    }

    /**
     * Scope to find active artists.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Attach a category to this artist.
     */
    public function attachCategory(Category $category, ?int $userId = null): void
    {
        $this->categories()->attach($category->id, [
            'created_by' => $userId ?? auth()->id(),
            'updated_by' => $userId ?? auth()->id(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Sync categories by type.
     */
    public function syncCategoriesByType(CategoryType $type, array $categoryIds): void
    {
        $existingOtherTypes = $this->categories()
            ->where('type', '!=', $type)
            ->pluck('categories.id')
            ->toArray();

        $this->categories()->sync(array_merge($existingOtherTypes, $categoryIds));
    }

    /**
     * Get categories grouped by type.
     */
    public function getCategoriesByType(): array
    {
        return $this->categories()
            ->get()
            ->groupBy('type')
            ->map(function ($categories) {
                return $categories->pluck('name', 'id');
            })
            ->toArray();
    }

    /**
     * Get the artist's display name with album count.
     */
    public function getDisplayNameAttribute(): string
    {
        $albumCount = $this->albums()->count();
        return "{$this->name} ({$albumCount} albums)";
    }

    /**
     * Get the primary genre for this artist.
     */
    public function getPrimaryGenreAttribute(): ?Category
    {
        return $this->genres()->first();
    }

    /**
     * Get the artist's years active.
     */
    public function getYearsActiveAttribute(): string
    {
        if (!$this->formed_year) {
            return 'Unknown';
        }

        $endYear = $this->is_active ? 'Present' : 'Unknown';
        return "{$this->formed_year} - {$endYear}";
    }
}
