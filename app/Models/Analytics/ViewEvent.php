<?php

declare(strict_types=1);

namespace App\Models\Analytics;

use App\Enums\SecondaryKeyType;
use App\Models\Customer;
use App\Models\User;
use App\Traits\HasSecondaryUniqueKey;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use Wildside\Userstamps\Userstamps;

class ViewEvent extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'view_events';

    /**
     * Get the secondary key type for this model.
     * Using Snowflake for high-performance analytics.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::SNOWFLAKE;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'viewable_type',
        'viewable_id',
        'user_id',
        'customer_id',
        'viewed_at',
        'view_duration_seconds',
        'page_url',
        'referrer_url',
        'device_type',
        'browser_type',
        'ip_address',
        'user_agent',
        'session_id',
        'is_unique_view',
        'scroll_percentage',
        'interactions_count',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'viewed_at' => 'datetime',
            'view_duration_seconds' => 'integer',
            'is_unique_view' => 'boolean',
            'scroll_percentage' => 'decimal:2',
            'interactions_count' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // Relationships
    public function viewable(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Scopes
    public function scopeForModel($query, string $modelType)
    {
        return $query->where('viewable_type', $modelType);
    }

    public function scopeUniqueViews($query)
    {
        return $query->where('is_unique_view', true);
    }

    public function scopeEngaged($query, int $minDuration = 30)
    {
        return $query->where('view_duration_seconds', '>=', $minDuration);
    }

    public function scopeByDateRange($query, Carbon $start, Carbon $end)
    {
        return $query->whereBetween('viewed_at', [$start, $end]);
    }

    public function scopeByDevice($query, string $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    public function scopePopularContent($query, string $modelType = null, int $limit = 50)
    {
        $query = $query->select('viewable_type', 'viewable_id')
            ->selectRaw('COUNT(*) as view_count')
            ->selectRaw('COUNT(DISTINCT user_id) as unique_viewers')
            ->selectRaw('AVG(view_duration_seconds) as avg_duration')
            ->groupBy('viewable_type', 'viewable_id');

        if ($modelType) {
            $query->where('viewable_type', $modelType);
        }

        return $query->orderByDesc('view_count')->limit($limit);
    }

    // Accessors & Mutators
    public function getIsEngagedAttribute(): bool
    {
        return $this->view_duration_seconds >= 30;
    }

    public function getFormattedDurationAttribute(): string
    {
        $minutes = intval($this->view_duration_seconds / 60);
        $seconds = $this->view_duration_seconds % 60;

        return sprintf('%d:%02d', $minutes, $seconds);
    }

    // Helper Methods
    public static function recordView(
        Model $viewable,
        array $context = [],
    ): self
    {
        $userId = $context['user_id'] ?? auth()->id();

        // Check if this is a unique view (first view by this user for this content)
        $isUniqueView = !self::where('viewable_type', get_class($viewable))
            ->where('viewable_id', $viewable->id)
            ->where('user_id', $userId)
            ->exists();

        return self::create([
            'viewable_type' => get_class($viewable),
            'viewable_id' => $viewable->id,
            'user_id' => $userId,
            'customer_id' => $context['customer_id'] ?? null,
            'viewed_at' => now(),
            'page_url' => $context['page_url'] ?? request()->url(),
            'referrer_url' => $context['referrer_url'] ?? request()->header('referer'),
            'device_type' => $context['device_type'] ?? 'web',
            'browser_type' => $context['browser_type'] ?? null,
            'ip_address' => $context['ip_address'] ?? request()->ip(),
            'user_agent' => $context['user_agent'] ?? request()->userAgent(),
            'session_id' => $context['session_id'] ?? session()->getId(),
            'is_unique_view' => $isUniqueView,
        ]);
    }

    public function updateEngagement(
        int   $durationSeconds,
        float $scrollPercentage = null,
        int   $interactionsCount = null,
    ): void
    {
        $this->update([
            'view_duration_seconds' => $durationSeconds,
            'scroll_percentage' => $scrollPercentage,
            'interactions_count' => $interactionsCount,
        ]);
    }
}
