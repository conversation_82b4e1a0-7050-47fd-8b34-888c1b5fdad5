<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\Categorizable;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use Wildside\Userstamps\Userstamps;

class Track extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;
    use Categorizable;

    /**
     * The table associated with the model.
     */
    protected $table = 'tracks';

    /**
     * Get the secondary key type for this model.
     * Using Snowflake for tracks - high performance for large datasets.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::SNOWFLAKE;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'album_id',
        'media_type_id',
        'composer',
        'milliseconds',
        'bytes',
        'unit_price',
        'track_number',
        'disc_number',
        'is_explicit',
        'is_active',
        'preview_url',
        'lyrics',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'milliseconds' => 'integer',
            'bytes' => 'integer',
            'track_number' => 'integer',
            'disc_number' => 'integer',
            'is_explicit' => 'boolean',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the album that owns the track.
     */
    public function album(): BelongsTo
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Get the media type that owns the track.
     */
    public function mediaType(): BelongsTo
    {
        return $this->belongsTo(MediaType::class);
    }

    /**
     * Get the artist through the album relationship.
     */
    public function artist(): BelongsTo
    {
        return $this->album()->artist();
    }

    /**
     * Get the invoice lines for the track.
     */
    public function invoiceLines(): HasMany
    {
        return $this->hasMany(InvoiceLine::class);
    }

    /**
     * Get the playlists that contain this track.
     */
    public function playlists(): BelongsToMany
    {
        return $this->belongsToMany(Playlist::class, 'playlist_track')
            ->withTimestamps()
            ->withPivot(['position', 'added_by']);
    }

    /**
     * Polymorphic relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['created_by', 'updated_by']);
    }

    /**
     * Get genres (categories of type GENRE).
     */
    public function genres(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::GENRE);
    }

    /**
     * Get moods (categories of type MOOD).
     */
    public function moods(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::MOOD);
    }

    /**
     * Get themes (categories of type THEME).
     */
    public function themes(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::THEME);
    }

    /**
     * Get instruments (categories of type INSTRUMENT).
     */
    public function instruments(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::INSTRUMENT);
    }

    /**
     * Get languages (categories of type LANGUAGE).
     */
    public function languages(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::LANGUAGE);
    }

    /**
     * Get occasions (categories of type OCCASION).
     */
    public function occasions(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::OCCASION);
    }

    /**
     * Scope to find tracks by genre.
     */
    public function scopeByGenre(Builder $query, string $genreName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($genreName) {
            $q->where('type', CategoryType::GENRE)
                ->where('name', 'like', "%{$genreName}%");
        });
    }

    /**
     * Scope to find tracks by mood.
     */
    public function scopeByMood(Builder $query, string $moodName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($moodName) {
            $q->where('type', CategoryType::MOOD)
                ->where('name', 'like', "%{$moodName}%");
        });
    }

    /**
     * Scope to find tracks by theme.
     */
    public function scopeByTheme(Builder $query, string $themeName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($themeName) {
            $q->where('type', CategoryType::THEME)
                ->where('name', 'like', "%{$themeName}%");
        });
    }

    /**
     * Scope to find tracks by instrument.
     */
    public function scopeByInstrument(Builder $query, string $instrumentName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($instrumentName) {
            $q->where('type', CategoryType::INSTRUMENT)
                ->where('name', 'like', "%{$instrumentName}%");
        });
    }

    /**
     * Scope to find tracks by language.
     */
    public function scopeByLanguage(Builder $query, string $languageName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($languageName) {
            $q->where('type', CategoryType::LANGUAGE)
                ->where('name', 'like', "%{$languageName}%");
        });
    }

    /**
     * Scope to find tracks by occasion.
     */
    public function scopeByOccasion(Builder $query, string $occasionName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($occasionName) {
            $q->where('type', CategoryType::OCCASION)
                ->where('name', 'like', "%{$occasionName}%");
        });
    }

    /**
     * Scope to find tracks by duration range.
     */
    public function scopeByDuration(Builder $query, int $minMs, int $maxMs): Builder
    {
        return $query->whereBetween('milliseconds', [$minMs, $maxMs]);
    }

    /**
     * Scope to find popular tracks (frequently purchased).
     */
    public function scopePopular(Builder $query, int $minPurchases = 10): Builder
    {
        return $query->whereHas('invoiceLines', function ($q) use ($minPurchases) {
            $q->havingRaw('COUNT(*) >= ?', [$minPurchases]);
        });
    }

    /**
     * Scope to find active tracks.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Sync categories by type.
     */
    public function syncCategoriesByType(CategoryType $type, array $categoryIds): void
    {
        $existingOtherTypes = $this->categories()
            ->where('type', '!=', $type)
            ->pluck('categories.id')
            ->toArray();

        $this->categories()->sync(array_merge($existingOtherTypes, $categoryIds));
    }

    /**
     * Get the track's duration in human-readable format.
     */
    public function getDurationAttribute(): string
    {
        $seconds = intval($this->milliseconds / 1000);
        $minutes = intval($seconds / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%d:%02d', $minutes, $remainingSeconds);
    }

    /**
     * Get the track's file size in human-readable format.
     */
    public function getFileSizeAttribute(): string
    {
        if (!$this->bytes) {
            return 'Unknown';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->bytes;
        $i = 0;

        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the primary genre for this track.
     */
    public function getPrimaryGenreAttribute(): ?Category
    {
        return $this->genres()->first();
    }

    /**
     * Get the track's full name with artist and album.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->artist->name} - {$this->album->title} - {$this->name}";
    }
}
