<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ChinookDatabaseSeeder extends Seeder
{
    /**
     * Seed the Chinook database.
     */
    public function run(): void
    {
        $this->command->info('🎵 Chinook Database Seeding Options:');
        $this->command->info('1. Factory-based seeding (existing)');
        $this->command->info('2. SQL dump seeding (new - complete Chinook dataset)');

        // Check if we should use SQL dump seeding
        if ($this->command->confirm('Use complete Chinook SQL dump data instead of factory-generated data?', false)) {
            $this->command->info('🚀 Using Chinook SQL Dump Seeder...');
            $this->call([
                \Database\Seeders\ChinookSqlDumpSeeder::class,
            ]);
        } else {
            $this->command->info('🏭 Using Factory-based Seeding...');
            $this->call([
                // Step 2: Independent tables
                CategorySeeder::class,  // NEW: Replaces GenreSeeder with closure table support
                MediaTypeSeeder::class,
                EmployeeSeeder::class,
                ArtistSeeder::class,

                // Step 3: Dependent tables
                AlbumSeeder::class,
                CustomerSeeder::class,
                PlaylistSeeder::class,

                // Step 4: Relationship tables
                TrackSeeder::class,
                InvoiceSeeder::class,

                // Step 5: Junction tables
                InvoiceLineSeeder::class,
                PlaylistTrackSeeder::class,
                CategorizableSeeder::class,  // NEW: For polymorphic category relationships
            ]);
        }
    }
}
