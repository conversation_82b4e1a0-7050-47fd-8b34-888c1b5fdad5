<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Invoice;
use App\Models\InvoiceLine;
use App\Models\Track;
use Illuminate\Database\Seeder;

class InvoiceLineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $invoices = Invoice::all();
        $tracks = Track::all();

        if ($invoices->isNotEmpty() && $tracks->isNotEmpty()) {
            foreach ($invoices as $invoice) {
                $lineCount = rand(1, 10);
                $selectedTracks = $tracks->random($lineCount);
                $total = 0;

                foreach ($selectedTracks as $track) {
                    $quantity = rand(1, 3);
                    $unitPrice = $track->unit_price;

                    InvoiceLine::create([
                        'invoice_id' => $invoice->id,
                        'track_id' => $track->id,
                        'unit_price' => $unitPrice,
                        'quantity' => $quantity,
                    ]);

                    $total += $unitPrice * $quantity;
                }

                // Update invoice total
                $invoice->update(['total' => $total]);
            }
        }
    }
}
