<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Album;
use App\Models\Genre;
use App\Models\MediaType;
use App\Models\Track;
use Illuminate\Database\Seeder;

class TrackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $albums = Album::all();
        $genres = Genre::all();
        $mediaTypes = MediaType::all();

        if ($albums->isNotEmpty() && $genres->isNotEmpty() && $mediaTypes->isNotEmpty()) {
            // Create tracks for each album
            foreach ($albums as $album) {
                Track::factory()
                    ->count(rand(8, 15)) // Random number of tracks per album
                    ->forAlbum($album)
                    ->recycle($genres)
                    ->recycle($mediaTypes)
                    ->create();
            }
        }
    }
}
