<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Album;
use App\Models\Artist;
use Illuminate\Database\Seeder;

class AlbumSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed some famous albums
        $albums = [
            ['id' => 1, 'title' => 'For Those About To Rock We Salute You', 'artist_id' => 1],
            ['id' => 4, 'title' => 'Let There Be Rock', 'artist_id' => 1],
            ['id' => 6, 'title' => 'Jagged Little Pill', 'artist_id' => 4],
            ['id' => 148, 'title' => 'Black Album', 'artist_id' => 50],
            ['id' => 183, 'title' => 'Dark Side Of The Moon', 'artist_id' => 120],
            ['id' => 185, 'title' => 'Greatest Hits I', 'artist_id' => 51],
        ];

        foreach ($albums as $album) {
            if (Artist::find($album['artist_id'])) {
                Album::updateOrCreate(['id' => $album['id']], $album);
            }
        }

        // Create additional albums using factory
        $artists = Artist::all();
        if ($artists->isNotEmpty() && Album::count() < 100) {
            Album::factory()
                ->count(100 - Album::count())
                ->recycle($artists)
                ->create();
        }
    }
}
