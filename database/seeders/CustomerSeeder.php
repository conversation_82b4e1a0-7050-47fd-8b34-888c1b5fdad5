<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Employee;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get available support representatives
        $supportReps = Employee::whereIn('title', [
            'Sales Support Agent',
            'Sales Manager',
        ])->get();

        if ($supportReps->isEmpty()) {
            $supportReps = Employee::all();
        }

        // Create customers using factory
        if ($supportReps->isNotEmpty()) {
            Customer::factory()
                ->count(100)
                ->recycle($supportReps)
                ->create();
        }
    }
}
