<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\MediaType;
use Illuminate\Database\Seeder;

class MediaTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mediaTypes = [
            ['id' => 1, 'name' => 'MPEG audio file'],
            ['id' => 2, 'name' => 'Protected AAC audio file'],
            ['id' => 3, 'name' => 'Protected MPEG-4 video file'],
            ['id' => 4, 'name' => 'Purchased AAC audio file'],
            ['id' => 5, 'name' => 'AAC audio file'],
        ];

        foreach ($mediaTypes as $mediaType) {
            MediaType::updateOrCreate(['id' => $mediaType['id']], $mediaType);
        }
    }
}
