# Chinook Database Analysis

## Table Structure and Data Volumes

Based on analysis of the SQL dump file, here are the tables and their approximate data volumes:

### Core Music Tables
1. **artists** - ~275 rows
   - Fields: `id`, `name`
   - No dependencies

2. **albums** - ~347 rows  
   - Fields: `id`, `title`, `artist_id`
   - Depends on: artists

3. **genres** - ~25 rows (TO BE CONVERTED TO CATEGORIES)
   - Fields: `id`, `name`
   - Will be converted to Category records with CategoryType::GENRE

4. **media_types** - ~5 rows
   - Fields: `id`, `name`
   - No dependencies

5. **tracks** - ~3,483 rows
   - Fields: `id`, `name`, `album_id`, `media_type_id`, `genre_id`, `composer`, `milliseconds`, `bytes`, `unit_price`
   - Depends on: albums, media_types, genres (categories)

### Customer & Employee Tables
6. **employees** - ~8 rows
   - Fields: `id`, `last_name`, `first_name`, `title`, `reports_to`, `birth_date`, `hire_date`, `address`, `city`, `state`, `country`, `postal_code`, `phone`, `fax`, `email`
   - Self-referencing (reports_to)

7. **customers** - ~59 rows
   - Fields: `id`, `first_name`, `last_name`, `company`, `address`, `city`, `state`, `country`, `postal_code`, `phone`, `fax`, `email`, `support_rep_id`
   - Depends on: employees (support_rep_id)

### Sales Tables
8. **invoices** - ~412 rows
   - Fields: `id`, `customer_id`, `invoice_date`, `billing_address`, `billing_city`, `billing_state`, `billing_country`, `billing_postal_code`, `total`
   - Depends on: customers

9. **invoice_lines** - ~2,240 rows
   - Fields: `id`, `invoice_id`, `track_id`, `unit_price`, `quantity`
   - Depends on: invoices, tracks

### Playlist Tables
10. **playlists** - ~18 rows
    - Fields: `id`, `name`
    - No dependencies

11. **playlist_track** - ~8,715 rows (Junction table)
    - Fields: `id`, `track_id`
    - Depends on: playlists, tracks

## Seeding Order (Dependency Chain)

1. **Independent Tables** (no foreign keys):
   - artists
   - media_types
   - employees (self-referencing handled internally)
   - playlists

2. **Genre Conversion**:
   - Convert genres → categories (CategoryType::GENRE)

3. **First Level Dependencies**:
   - albums (depends on artists)
   - customers (depends on employees)

4. **Second Level Dependencies**:
   - tracks (depends on albums, media_types, categories)
   - invoices (depends on customers)

5. **Junction/Relationship Tables**:
   - invoice_lines (depends on invoices, tracks)
   - playlist_track (depends on playlists, tracks)

## Special Considerations

### Genre to Category Conversion
The original `genres` table will be converted to use the existing polymorphic Category system:
- Each genre becomes a Category with `type = CategoryType::GENRE`
- Maintains original IDs for foreign key mapping
- Integrates with existing closure table hierarchy

### Data Integrity
- All seeders will use database transactions
- Foreign key constraints must be respected
- Large datasets (tracks, playlist_track, invoice_lines) need memory management

### Factory Integration
Existing factories should be used where possible:
- ArtistFactory
- AlbumFactory  
- CategoryFactory (for genre conversion)
- CustomerFactory
- EmployeeFactory
- InvoiceFactory
- InvoiceLineFactory
- MediaTypeFactory
- PlaylistFactory
- TrackFactory

## File Structure
All seeders will be created in: `database/sqldump/seeders/`
With namespace: `Database\Seeders`
